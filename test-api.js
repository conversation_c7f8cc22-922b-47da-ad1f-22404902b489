// Test script for API endpoints
// Run with: node test-api.js

const testChatAPI = async () => {
  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log('🧪 Testing Chat Box API...');
    
    // Test 1: Create new chat
    const response = await fetch(`${baseUrl}/api/platform/chat-box-generate?chatId=new`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'Hello, this is a test message',
        module: 'test',
        metadata: {
          selectedTickets: [{ ticket_id: 'TEST-123', ticket_title: 'Test' }],
          additionalInput: 'Test input'
        }
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API Response:', data);
      console.log('✅ Chat ID:', data.chatId);
    } else {
      console.log('❌ API Error:', response.status, response.statusText);
      const errorData = await response.text();
      console.log('Error details:', errorData);
    }
    
  } catch (error) {
    console.log('❌ Network Error:', error.message);
  }
};

// Uncomment to run:
// testChatAPI();
