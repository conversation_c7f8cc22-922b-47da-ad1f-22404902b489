# ChatBox Component

A reusable chat interface component that follows the project's established patterns for API integration, error handling, and user experience.

## Features

- **Real-time messaging** with AI-powered responses
- **Consistent styling** following project design patterns
- **Error handling** with user-friendly error messages
- **Internationalization** support using next-intl
- **Accessibility** features built-in
- **TypeScript** support with full type safety
- **Customizable** appearance and behavior
- **React Query** integration for efficient data management

## Basic Usage

```tsx
import ChatBox from '@/components/chat-box';

function MyComponent() {
  const chatId = "your-chat-id";
  
  return (
    <ChatBox
      chatId={chatId}
      placeholder="Type your message..."
    />
  );
}
```

## Advanced Usage

```tsx
import ChatBox from '@/components/chat-box';
import { ChatBoxMessage } from '@/modules/platform/utils/hooks/use-chat-box';

function AdvancedChatComponent() {
  const chatId = "your-chat-id";
  
  const handleMessageSent = (message: ChatBoxMessage) => {
    console.log('User sent:', message);
    // Add custom logic here
  };
  
  const handleMessageReceived = (message: ChatBoxMessage) => {
    console.log('AI responded:', message);
    // Add custom logic here
  };
  
  return (
    <ChatBox
      chatId={chatId}
      placeholder="Ask me anything..."
      onMessageSent={handleMessageSent}
      onMessageReceived={handleMessageReceived}
      enableMarkdown={true}
      enableCodeHighlighting={true}
      maxTokens={2000}
      temperature={0.7}
      className="custom-chat-styling"
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `chatId` | `string` | **Required** | Unique identifier for the chat session |
| `placeholder` | `string` | `"Type your message here..."` | Placeholder text for the input field |
| `className` | `string` | `""` | Additional CSS classes for styling |
| `onMessageSent` | `(message: ChatBoxMessage) => void` | `undefined` | Callback when user sends a message |
| `onMessageReceived` | `(message: ChatBoxMessage) => void` | `undefined` | Callback when AI responds |
| `enableMarkdown` | `boolean` | `true` | Enable markdown formatting in messages |
| `enableCodeHighlighting` | `boolean` | `true` | Enable syntax highlighting for code blocks |
| `maxTokens` | `number` | `1000` | Maximum tokens for AI responses |
| `temperature` | `number` | `0.7` | AI response creativity (0.0 - 1.0) |

## API Integration

The ChatBox component integrates with the backend through:

1. **API Endpoint**: `/api/platform/chat-box-generate`
2. **Custom Hook**: `useChatBoxGenerate` for message generation
3. **Data Fetching**: `useChatBoxMessages` for message history
4. **Error Handling**: Consistent with project patterns

### API Request Format

```typescript
interface ChatBoxGenerateRequest {
  prompt: string;
  context?: string;
  messageType?: 'text' | 'code' | 'markdown';
  temperature?: number;
  maxTokens?: number;
}
```

### API Response Format

```typescript
interface ChatBoxGenerateResponse {
  id: string;
  message: string;
  messageType: string;
  timestamp: string;
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
  };
}
```

## Styling

The component uses Tailwind CSS classes and follows the project's design system:

- **Primary color**: `primary-teal-600`
- **Neutral colors**: `secondary-neutral-*` scale
- **Consistent spacing**: Following project patterns
- **Responsive design**: Mobile-friendly layout

## Error Handling

The component handles various error scenarios:

- **Network errors**: Displays user-friendly messages
- **Validation errors**: Shows inline validation feedback
- **API errors**: Graceful degradation with retry options
- **Loading states**: Clear loading indicators

## Internationalization

All user-facing text supports internationalization through next-intl:

```json
{
  "Platform": {
    "chat": {
      "generating": "Generating response",
      "messageRequired": "Message is required",
      "messagePlaceholder": "Type your message here...",
      "errorGenerating": "Error generating message"
    }
  }
}
```

## Testing

The component can be tested using the project's testing setup:

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import ChatBox from '@/components/chat-box';

test('renders chat input', () => {
  render(<ChatBox chatId="test-chat" />);
  expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
});
```

## Examples

See `example-usage.tsx` for various usage patterns:

- Simple chat implementation
- Code-focused chat
- Creative writing chat
- Custom styled chat

## Dependencies

- `@tanstack/react-query` - Data fetching and caching
- `react-hook-form` - Form management
- `@heroicons/react` - Icons
- `@heroui/react` - UI components
- `next-intl` - Internationalization
- `axios` - HTTP client

## Integration with Existing Project

This component follows the project's established patterns:

1. **API Routes**: Uses Next.js API routes pattern
2. **Authentication**: Integrates with `getAuthToken()`
3. **Error Handling**: Consistent error handling patterns
4. **Styling**: Uses project's Tailwind configuration
5. **State Management**: Uses React Query for data management
6. **TypeScript**: Full type safety throughout
