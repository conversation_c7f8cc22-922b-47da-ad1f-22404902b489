// Simple test to check if imports work
import React from 'react';
import ChatBox from './index';
import { useChatBoxGenerate, useChatActions } from '@/modules/platform/utils/hooks/use-chat-box';

const TestComponent = () => {
  console.log('✅ ChatBox component imported successfully');
  console.log('✅ Hooks imported successfully');
  
  return (
    <div>
      <h1>Import Test Successful</h1>
      <p>Check console for confirmation</p>
    </div>
  );
};

export default TestComponent;
