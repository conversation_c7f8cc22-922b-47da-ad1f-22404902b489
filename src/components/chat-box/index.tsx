import React, { useRef, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import { PaperAirplaneIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { Divider } from '@heroui/react';
import Textarea from '@/components/textarea';
import Button from '@/components/button';
import UserAvatar from '@/components/user-avatar';
import { ButtonVariant } from '@/components/button/types';
import {
  useChatBoxGenerate,
  useChatBoxMessages,
  ChatBoxMessage,
  ChatBoxGenerateRequest,
  validateChatInput,
  formatChatMessage,
} from '@/modules/platform/utils/hooks/use-chat-box';
import log from '@/utils/logger';

export interface ChatBoxProps {
  chatId: string;
  placeholder?: string;
  className?: string;
  onMessageSent?: (message: ChatBoxMessage) => void;
  onMessageReceived?: (message: ChatBoxMessage) => void;
  enableMarkdown?: boolean;
  enableCodeHighlighting?: boolean;
  maxTokens?: number;
  temperature?: number;
}

interface ChatFormData {
  message: string;
}

const ChatBox: React.FC<ChatBoxProps> = ({
  chatId,
  placeholder,
  className = '',
  onMessageSent,
  onMessageReceived,
  enableMarkdown = true,
  enableCodeHighlighting = true,
  maxTokens = 1000,
  temperature = 0.7,
}) => {
  const chatConstants = useTranslations('Platform.chat');
  const commonConstants = useTranslations('Common');
  
  const { control, handleSubmit, reset, watch, formState: { errors } } = useForm<ChatFormData>({
    defaultValues: { message: '' }
  });
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [localMessages, setLocalMessages] = useState<ChatBoxMessage[]>([]);

  // Watch the form field value for UI updates
  const messageValue = watch('message');

  // Custom hooks for API calls
  const { generateMessage, isGenerating, error: generateError } = useChatBoxGenerate(chatId);
  const { data: messages, isLoading: isLoadingMessages, error: fetchError } = useChatBoxMessages(chatId);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [localMessages, messages]);

  // Update local messages when API data changes
  useEffect(() => {
    if (messages) {
      setLocalMessages(messages);
    }
  }, [messages]);

  const handleSendMessage = async (data: ChatFormData) => {
    const { isValid, error: validationError } = validateChatInput(data.message);
    
    if (!isValid) {
      log.warn('Invalid chat input:', validationError);
      return;
    }

    try {
      // Add user message to local state immediately for better UX
      const userMessage = formatChatMessage(data.message, 'user');
      setLocalMessages(prev => [...prev, userMessage]);
      onMessageSent?.(userMessage);

      // Prepare request data
      const requestData: ChatBoxGenerateRequest = {
        prompt: data.message,
        messageType: enableMarkdown ? 'markdown' : 'text',
        maxTokens,
        temperature,
      };

      // Generate AI response
      generateMessage(requestData, {
        onSuccess: (response) => {
          const aiMessage: ChatBoxMessage = {
            id: response.id,
            message: response.message,
            messageType: response.messageType as 'text' | 'code' | 'markdown',
            timestamp: response.timestamp,
            sender: 'ai',
            metadata: response.metadata,
          };
          
          setLocalMessages(prev => [...prev, aiMessage]);
          onMessageReceived?.(aiMessage);
          reset(); // Clear the input field
        },
        onError: (error) => {
          log.error('Failed to generate message:', error);
          // Optionally add error message to chat
          const errorMessage = formatChatMessage(
            'Sorry, I encountered an error while processing your message. Please try again.',
            'ai'
          );
          setLocalMessages(prev => [...prev, errorMessage]);
        },
      });

    } catch (error) {
      log.error('Error in handleSendMessage:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(handleSendMessage)();
    }
  };

  const renderMessage = (msg: ChatBoxMessage) => {
    if (msg.sender === 'user') {
      return (
        <div key={msg.id} className="flex justify-end mb-4">
          <div className="flex items-start gap-3 max-w-3xl">
            <div className="bg-primary-teal-600 text-white rounded-2xl px-4 py-3">
              <p className="text-sm leading-relaxed">{msg.message}</p>
            </div>
            <UserAvatar sizeClassName="w-8 h-8" />
          </div>
        </div>
      );
    } else {
      return (
        <div key={msg.id} className="flex justify-start mb-4">
          <div className="flex items-start gap-3 max-w-4xl w-full">
            <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
              <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
            </div>
            <div className="bg-white rounded-2xl p-4 border flex-1">
              <p className="text-neutral-900 text-sm leading-relaxed">{msg.message}</p>
              {msg.metadata && (
                <div className="mt-2 text-xs text-secondary-neutral-500">
                  {msg.metadata.tokensUsed && (
                    <span>Tokens: {msg.metadata.tokensUsed}</span>
                  )}
                  {msg.metadata.processingTime && (
                    <span className="ml-2">Time: {msg.metadata.processingTime}ms</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }
  };

  if (fetchError) {
    return (
      <div className="flex items-center justify-center h-64 text-red-500">
        <p>Error loading chat messages. Please try again.</p>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full w-full space-y-6 ${className}`}>
      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto space-y-2 p-4">
        {isLoadingMessages ? (
          <div className="flex items-center justify-center h-32">
            <p className="text-secondary-neutral-500">{commonConstants('loading')}</p>
          </div>
        ) : (
          <>
            {localMessages.map(renderMessage)}
            {isGenerating && (
              <div className="flex justify-start mb-4">
                <div className="flex items-start gap-3">
                  <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
                    <SparklesIcon className="h-5 w-5 text-primary-teal-600 animate-pulse" />
                  </div>
                  <div className="bg-white rounded-2xl p-4 border">
                    <p className="text-secondary-neutral-500 text-sm">
                      {chatConstants('generating')}...
                    </p>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Section */}
      <div className="border border-gray-200 rounded-xl bg-white p-2">
        <Controller
          name="message"
          control={control}
          rules={{ 
            required: chatConstants('messageRequired'),
            validate: (value) => {
              const { isValid, error } = validateChatInput(value);
              return isValid || error;
            }
          }}
          render={({ field }) => (
            <div className="relative w-full">
              <Textarea
                {...field}
                placeholder={placeholder || chatConstants('messagePlaceholder')}
                className="rounded-xl border-none w-full resize-none focus:ring-0 min-h-[60px]"
                isInvalid={!!errors.message}
                errorMessage={errors.message?.message}
                isDisabled={isGenerating}
                onKeyDown={handleKeyDown}
              />
              <div className="absolute right-4 bottom-4 flex items-center">
                <Button
                  variant={ButtonVariant.GHOST}
                  isIconOnly
                  isDisabled={!messageValue?.trim() || isGenerating}
                  onClick={handleSubmit(handleSendMessage)}
                  className="p-2"
                >
                  <PaperAirplaneIcon 
                    className={`h-5 w-5 ${
                      messageValue?.trim() && !isGenerating 
                        ? 'text-primary-teal-600' 
                        : 'text-secondary-neutral-400'
                    }`} 
                  />
                </Button>
              </div>
            </div>
          )}
        />
      </div>

      {/* Error Display */}
      {(generateError || fetchError) && (
        <div className="text-red-500 text-sm p-2 bg-red-50 rounded-lg">
          {generateError?.message || fetchError?.message || 'An error occurred'}
        </div>
      )}
    </div>
  );
};

export default ChatBox;
