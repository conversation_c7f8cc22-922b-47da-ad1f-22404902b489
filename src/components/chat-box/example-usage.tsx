import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { useTranslations } from 'next-intl';
import ChatBox from './index';
import { ChatBoxMessage } from '@/modules/platform/utils/hooks/use-chat-box';
import log from '@/utils/logger';

/**
 * Example component showing how to use the ChatBox component
 * This follows the project's patterns for component structure and usage
 */
const ChatBoxExample: React.FC = () => {
  const router = useRouter();
  const { 'chat-id': chatId, module } = router.query;
  const chatConstants = useTranslations('Platform.chat');

  const [messageCount, setMessageCount] = useState(0);
  const [currentChatId, setCurrentChatId] = useState<string | undefined>(chatId as string);

  // Example metadata that would come from your module context
  const exampleMetadata = {
    selectedTickets: [
      { ticket_id: 'TICKET-123', ticket_title: 'Implement payment gateway' },
      { ticket_id: 'TICKET-124', ticket_title: 'Add user authentication' }
    ],
    additionalInput: 'Focus on security best practices',
    figmaLinks: ['https://figma.com/design/example'],
    contextRepositories: ['frontend-repo', 'backend-api'],
    selectedRepository: 'frontend-repo',
    branchName: 'feature/payment-integration'
  };

  const handleMessageSent = (message: ChatBoxMessage) => {
    log.info('Message sent:', message);
    setMessageCount(prev => prev + 1);

    // You can add custom logic here, such as:
    // - Analytics tracking
    // - Custom validation
    // - UI updates
    // - State management
  };

  const handleMessageReceived = (message: ChatBoxMessage) => {
    log.info('Message received:', message);

    // You can add custom logic here, such as:
    // - Notifications
    // - Custom processing of AI responses
    // - Integration with other components
    // - State updates
  };

  const handleChatCreated = (newChatId: string) => {
    log.info('New chat created:', newChatId);
    setCurrentChatId(newChatId);

    // Update URL or state management
    router.push(`/chat/${newChatId}`, undefined, { shallow: true });
  };

  return (
    <div className="h-full w-full flex flex-col">
      {/* Header */}
      <div className="border-b border-secondary-neutral-200 p-4">
        <h2 className="text-lg font-semibold text-neutral-900">
          {chatConstants('chatTitle')}
        </h2>
        <p className="text-sm text-secondary-neutral-600">
          {currentChatId ? `Chat ID: ${currentChatId}` : 'New Chat'} |
          Module: {module || 'General'} |
          Messages: {messageCount}
        </p>
      </div>

      {/* Chat Box */}
      <div className="flex-1 p-4">
        <ChatBox
          chatId={currentChatId}
          module={module as string}
          metadata={exampleMetadata}
          placeholder={chatConstants('enterMessage')}
          onMessageSent={handleMessageSent}
          onMessageReceived={handleMessageReceived}
          onChatCreated={handleChatCreated}
          enableMarkdown={true}
          enableCodeHighlighting={true}
          maxTokens={1500}
          temperature={0.7}
          className="h-full"
        />
      </div>
    </div>
  );
};

export default ChatBoxExample;

/**
 * Alternative usage patterns for different scenarios:
 */

// 1. Simple chat without callbacks
export const SimpleChatBox: React.FC<{ chatId: string }> = ({ chatId }) => (
  <ChatBox
    chatId={chatId}
    placeholder="Type your message..."
  />
);

// 2. Code-focused chat
export const CodeChatBox: React.FC<{ chatId: string }> = ({ chatId }) => (
  <ChatBox
    chatId={chatId}
    placeholder="Ask about code or request code generation..."
    enableCodeHighlighting={true}
    maxTokens={2000}
    temperature={0.3} // Lower temperature for more focused code responses
  />
);

// 3. Creative writing chat
export const CreativeChatBox: React.FC<{ chatId: string }> = ({ chatId }) => (
  <ChatBox
    chatId={chatId}
    placeholder="Let's create something together..."
    enableMarkdown={true}
    maxTokens={3000}
    temperature={0.9} // Higher temperature for more creative responses
  />
);

// 4. Chat with custom styling
export const CustomStyledChatBox: React.FC<{ chatId: string }> = ({ chatId }) => (
  <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4">
    <ChatBox
      chatId={chatId}
      placeholder="Custom styled chat..."
      className="bg-white rounded-lg shadow-lg"
    />
  </div>
);
