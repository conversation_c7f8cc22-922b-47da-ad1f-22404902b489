import React, { useState } from 'react';
import ChatBox from '@/components/chat-box';
import { ChatBoxMessage } from '@/modules/platform/utils/hooks/use-chat-box';

const TestChatPage: React.FC = () => {
  const [currentChatId, setCurrentChatId] = useState<string | undefined>();

  const handleChatCreated = (chatId: string) => {
    console.log('✅ New chat created:', chatId);
    setCurrentChatId(chatId);
  };

  const handleMessageSent = (message: ChatBoxMessage) => {
    console.log('📤 Message sent:', message);
  };

  const handleMessageReceived = (message: ChatBoxMessage) => {
    console.log('📥 Message received:', message);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4">Chat Box Test</h1>
        
        <div className="bg-white rounded-lg shadow-lg h-96">
          <ChatBox
            chatId={currentChatId}
            module="test"
            metadata={{
              selectedTickets: [
                { ticket_id: 'TEST-123', ticket_title: 'Test ticket' }
              ],
              additionalInput: 'This is a test',
              selectedRepository: 'test-repo'
            }}
            placeholder="Type a test message..."
            onChatCreated={handleChatCreated}
            onMessageSent={handleMessageSent}
            onMessageReceived={handleMessageReceived}
            className="h-full"
          />
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded">
          <h3 className="font-semibold">Debug Info:</h3>
          <p>Current Chat ID: {currentChatId || 'None (will create new)'}</p>
          <p>Check browser console for logs</p>
        </div>
      </div>
    </div>
  );
};

export default TestChatPage;
