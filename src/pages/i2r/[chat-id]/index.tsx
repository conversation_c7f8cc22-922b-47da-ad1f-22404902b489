import Layout, { LayoutType } from '@/components/layout';
import { getEnvConfig } from '@/env-config.zod';
import I2ROutput from '@/modules/i2r/components/output';
import { I2RPollingProvider } from '@/modules/i2r/contexts/polling.context';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { I2R_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { Modules } from '@/modules/platform/interfaces/modules';
import { getMessages } from '@/utils/i18n';
import { getPMPConfig } from '@/utils/server/platform/config/api';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next/types';
import React, { useState } from 'react';
import { withAuthentication } from '@/utils/hooks/with-authentication';
import { getChatByChatId } from '@/utils/server/platform/chat/api';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';
import { useRequestHistoryTabItems } from '@/modules/platform/utils/hooks/request-history-tab-items';
import UserMessage from '@/components/user-message';
import { useFetchChat } from '@/modules/platform/utils/hooks/fetch-chat';
import log from '@/utils/logger';
import { ChatId } from '@/components/chat_id/chat_id';
import ChatBox from '@/modules/i2r/components/chat-box';
import { ChatMessage } from '@/modules/i2r/components/chat-box';
import Split from 'react-split';
import { XMarkIcon } from '@heroicons/react/24/outline';

const I2ROutputPage = () => {
  const router = useRouter();
  const pathname = router.pathname;
  const { 'chat-id': chatId } = router.query;

  const [messages, setMessages] = useState<ChatMessage[]>([
   {
      id: '1',
      sender: 'user',
      text: 'I am working on a secure payment gateway implementation, what would be the best features for it?'
    },
    {
      id: '2',
      sender: 'system',
      text: 'Amazing! Here are some feature ideas that might work...',
    },
    {
      id: '3',
      sender: 'ai',
      text: 'Product Overview: The Secure Payment Gateway Integration project aims to develop and integrate secure payment gateway options for processing transactions across multiple platforms...',
      showActions: true
    }
  ]);

  const handleSend = (msg: string) => {
    setMessages((prev) => [
      ...prev,
      { id: Date.now().toString(), sender: 'user' as const, text: msg },
      {
        id: (Date.now() + 1).toString(),
        sender: 'ai' as const,
        text: 'This is an AI response.',
        showActions: true
      }
    ]);
  };

  const commonConstants = useTranslations('Common');
  const i2rConstants = useTranslations('I2R.homepage');

  const { activeRequests, archivedRequests, refetchRequestHistory } = useRequestHistory(
    Modules.I2R,
  );

  const { data: chatMessage, isLoading, isError } = useFetchChat(chatId as string);

  const onCreateNewRequest = () => router.push(`/${Modules.I2R.toLocaleLowerCase()}`);

  const [isRightPanelOpen, setIsRightPanelOpen] = useState(false);

  return (
    <I2RPollingProvider>
      <div className="flex h-full gap-6">
        <ModuleSidebar
          breadcrumbItems={I2R_BREADCRUMBS.map((item) => {
            return { ...item, children: commonConstants(item.children) };
          })}
          tabItems={useRequestHistoryTabItems(
            activeRequests,
            archivedRequests,
            refetchRequestHistory,
            Modules.I2R,
            pathname,
            onCreateNewRequest,
          )}
          activeRequests={activeRequests}
          archivedRequests={archivedRequests}
        />

        {/* Split Panel for Chat and PRD Output */}
        <Split
          className="flex w-full gap-4"
          sizes={isRightPanelOpen ? [50, 50] : [100, 0]}
          minSize={0}
          gutterSize={8}
          direction="horizontal"
        >
          {/* Left Panel - Chat */}
          <div className="flex flex-col gap-6 rounded-lg border p-4 h-full">
            <div className='flex h-full flex-col gap-4 overflow-hidden rounded-xl border bg-secondary-neutral-50 p-4'>
              <div className="flex items-center gap-4 mb-2">
                <div className="rounded-full border border-secondary-neutral-200 bg-white p-1">
                  <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
                </div>
                <ChatId chatId={chatId as string} />
              </div>
              <div className="flex-1 overflow-y-auto">
                <ChatBox
                  messages={messages}
                  onSend={handleSend}
                  onOpenRightPanel={() => setIsRightPanelOpen(true)}
                  chatId={chatId as string}
                />
              </div>
              {/* <UserMessage message={chatMessage ?? {}} isLoading={isLoading} isError={isError} /> */}
            </div>
          </div>
          {/* Right Panel - PRD Output */}
          {isRightPanelOpen ? (
            <div
              className="relative flex flex-col gap-6 rounded-lg border p-4 h-full"
            >
              <div className='flex h-full flex-col gap-4 overflow-y-auto rounded-xl border bg-secondary-neutral-50 p-4'>
              <button
                className="left-2 top-2 z-10 flex h-8 w-8 items-center justify-center rounded-full border border-secondary-neutral-200 bg-white hover:bg-secondary-neutral-100"
                onClick={() => setIsRightPanelOpen(false)}
                aria-label="Close"
                type="button"
              >
                <XMarkIcon className="h-5 w-5 text-secondary-neutral-500" />
              </button>
                <div className="flex items-center gap-4 mb-2">
                  <div className="rounded-full border border-secondary-neutral-200 bg-white p-1">
                    <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
                  </div>
                </div>
                {chatId && <I2ROutput chatId={chatId as string} userPrompt={chatMessage?.title} />}
              </div>
            </div>
          ) : (
            <div style={{ width: 0, minWidth: 0, maxWidth: 0, padding: 0, border: 'none' }} />
          )}
        </Split>
      </div>
    </I2RPollingProvider>
  );
};

export default Layout(I2ROutputPage, LayoutType.MAIN);

I2ROutputPage.messages = ['I2R', 'Platform', 'Common'];

export const getServerSideProps = withAuthentication(
  async (context: GetServerSidePropsContext, token: string) => {
    const { locale, query } = context;
    const { 'chat-id': chatId } = query;

    const envConfig = getEnvConfig();

    const apiBaseUrl = envConfig.backendUrl;

    try {
      await getChatByChatId(apiBaseUrl, token, chatId as string);
    } catch (error) {
      log.warn('Error occurred while fetching chat', error);
      return {
        redirect: {
          destination: `/${Modules.I2R.toLocaleLowerCase()}`,
          permanent: false,
        },
      };
    }

    const jiraConfig = await getPMPConfig(apiBaseUrl, token);

    return {
      props: {
        envConfig,
        jiraConfig,
        messages: await getMessages(locale ?? 'en', I2ROutputPage.messages),
      },
    };
  },
);
