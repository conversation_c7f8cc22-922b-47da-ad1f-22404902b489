import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import { getEnvConfig } from '@/env-config.zod';

const BACKEND_URL = getEnvConfig().backendUrl;

interface ChatActionRequest {
  action: 'like' | 'dislike' | 'regenerate';
  messageId: string;
  regenerationReason?: string;
}

interface ChatActionResponse {
  success: boolean;
  messageId: string;
  action: string;
  timestamp: string;
  newMessage?: {
    id: string;
    message: string;
    timestamp: string;
  };
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const requestData: ChatActionRequest = req.body;
    const token = await getAuthToken(req);

    // Validate required fields
    if (!chatId || !requestData.action || !requestData.messageId) {
      return res.status(400).json({
        error: 'Missing required fields: chatId, action, and messageId are required',
      });
    }

    // Validate action type
    if (!['like', 'dislike', 'regenerate'].includes(requestData.action)) {
      return res.status(400).json({
        error: 'Invalid action. Must be one of: like, dislike, regenerate',
      });
    }

    // Prepare the request payload
    const payload = {
      action: requestData.action,
      messageId: requestData.messageId,
      regenerationReason: requestData.regenerationReason,
    };

    // Make API call to backend
    const url = `${BACKEND_URL}/${API_PATHS.CHAT_MESSAGE_ACTION}/${chatId}`;
    const response = await axios.post(url, payload, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    // Format response
    const formattedResponse: ChatActionResponse = {
      success: true,
      messageId: requestData.messageId,
      action: requestData.action,
      timestamp: new Date().toISOString(),
      newMessage: response.data.newMessage ? {
        id: response.data.newMessage.id,
        message: response.data.newMessage.message,
        timestamp: response.data.newMessage.timestamp,
      } : undefined,
    };

    return res.status(200).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status ?? 500;
      const errorData = error.response?.data || { message: 'Backend service error' };
      
      return res.status(status).json({
        error: errorData.message || 'Failed to perform action',
        details: errorData,
      });
    } else {
      return res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'POST':
      return handlePost(req, res);

    default:
      res.setHeader('Allow', ['POST']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
