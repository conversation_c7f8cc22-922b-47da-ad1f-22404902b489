import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import { getEnvConfig } from '@/env-config.zod';

const BACKEND_URL = getEnvConfig().backendUrl;

interface ChatBoxGenerateRequest {
  prompt: string;
  context?: string;
  messageType?: 'text' | 'code' | 'markdown';
  temperature?: number;
  maxTokens?: number;
}

interface ChatBoxGenerateResponse {
  id: string;
  message: string;
  messageType: string;
  timestamp: string;
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
  };
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const requestData: ChatBoxGenerateRequest = req.body;
    const token = await getAuthToken(req);

    // Validate required fields
    if (!requestData.prompt || !chatId) {
      return res.status(400).json({
        error: 'Missing required fields: prompt and chatId are required',
      });
    }

    // Prepare the request payload following project patterns
    const payload = {
      prompt: requestData.prompt,
      context: requestData.context || '',
      messageType: requestData.messageType || 'text',
      temperature: requestData.temperature || 0.7,
      maxTokens: requestData.maxTokens || 1000,
      chatId: chatId,
    };

    // Make API call to backend
    const url = `${BACKEND_URL}/${API_PATHS.CHAT_MESSAGE_CREATE}/${chatId}`;
    const response = await axios.post(url, payload, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    // Format response following project patterns
    const formattedResponse: ChatBoxGenerateResponse = {
      id: response.data.id || `msg_${Date.now()}`,
      message: response.data.message || response.data.content || '',
      messageType: response.data.messageType || 'text',
      timestamp: response.data.timestamp || new Date().toISOString(),
      metadata: {
        tokensUsed: response.data.tokensUsed,
        processingTime: response.data.processingTime,
      },
    };

    return res.status(200).json(formattedResponse);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status ?? 500;
      const errorData = error.response?.data || { message: 'Backend service error' };
      
      return res.status(status).json({
        error: errorData.message || 'Failed to generate message',
        details: errorData,
      });
    } else {
      return res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId, messageId } = req.query;
    const token = await getAuthToken(req);

    if (!chatId) {
      return res.status(400).json({
        error: 'Missing required parameter: chatId',
      });
    }

    // If messageId is provided, get specific message, otherwise get all messages
    let url: string;
    if (messageId) {
      url = `${BACKEND_URL}/${API_PATHS.CHAT_MESSAGE}/${chatId}/message/${messageId}`;
    } else {
      url = `${BACKEND_URL}/${API_PATHS.CHAT_MESSAGE}/${chatId}/history`;
    }

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return res.status(200).json(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return res.status(error.response?.status ?? 500).json(error.response?.data);
    } else {
      return res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'POST':
      return handlePost(req, res);
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['POST', 'GET']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
