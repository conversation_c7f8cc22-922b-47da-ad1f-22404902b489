import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '@/utils/axios';
import log from '@/utils/logger';

export interface ChatBoxMessage {
  id: string;
  message: string;
  messageType: 'text' | 'code' | 'markdown';
  timestamp: string;
  sender: 'human' | 'ai';
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
    showActions?: boolean;
    liked?: boolean | null;
    disliked?: boolean | null;
  };
}

export interface ChatBoxGenerateRequest {
  prompt: string;
  module?: string;
  metadata?: {
    selectedTickets?: Array<{ ticket_id: string; ticket_title: string }>;
    additionalInput?: string;
    figmaLinks?: string[];
    contextRepositories?: string[];
    selectedRepository?: string;
    branchName?: string;
    [key: string]: any;
  };
  context?: string;
  messageType?: 'text' | 'code' | 'markdown';
  temperature?: number;
  maxTokens?: number;
}

export interface ChatBoxGenerateResponse {
  id: string;
  chatId: string;
  message: string;
  messageType: string;
  timestamp: string;
  sender: 'human' | 'ai';
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
    showActions?: boolean;
    liked?: boolean | null;
    disliked?: boolean | null;
  };
}

/**
 * Custom hook for generating chat messages using the chat-box API
 * Follows the project's pattern for API calls and error handling
 */
export const useChatBoxGenerate = (chatId?: string) => {
  const queryClient = useQueryClient();

  const generateMessage = useMutation<ChatBoxGenerateResponse, Error, ChatBoxGenerateRequest>({
    mutationFn: async (requestData: ChatBoxGenerateRequest) => {
      try {
        const response = await axiosInstance.post(
          '/api/platform/chat-box-generate',
          requestData,
          {
            params: { chatId: chatId || 'new' },
          },
        );
        return response.data;
      } catch (error) {
        log.error('Error generating chat message:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate and refetch chat messages to update the UI
      const actualChatId = data.chatId || chatId;
      if (actualChatId) {
        queryClient.invalidateQueries({ queryKey: ['chatMessages', actualChatId] });
      }
      log.info('Chat message generated successfully:', data.id);
    },
    onError: (error) => {
      log.error('Failed to generate chat message:', error);
    },
  });

  return {
    generateMessage: generateMessage.mutate,
    generateMessageAsync: generateMessage.mutateAsync,
    isGenerating: generateMessage.isPending,
    error: generateMessage.error,
    data: generateMessage.data,
    reset: generateMessage.reset,
  };
};

/**
 * Custom hook for fetching chat messages
 * Follows the project's pattern for data fetching
 */
export const useChatBoxMessages = (chatId: string, enabled: boolean = true) => {
  return useQuery<ChatBoxMessage[], Error>({
    queryKey: ['chatMessages', chatId],
    queryFn: async () => {
      try {
        const response = await axiosInstance.get('/api/platform/chat-box-generate', {
          params: { chatId },
        });
        return response.data;
      } catch (error) {
        log.error('Error fetching chat messages:', error);
        throw error;
      }
    },
    enabled: !!chatId && enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Custom hook for fetching a specific chat message
 */
export const useChatBoxMessage = (chatId: string, messageId: string, enabled: boolean = true) => {
  return useQuery<ChatBoxMessage, Error>({
    queryKey: ['chatMessage', chatId, messageId],
    queryFn: async () => {
      try {
        const response = await axiosInstance.get('/api/platform/chat-box-generate', {
          params: { chatId, messageId },
        });
        return response.data;
      } catch (error) {
        log.error('Error fetching chat message:', error);
        throw error;
      }
    },
    enabled: !!chatId && !!messageId && enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Utility function to format messages for display
 */
export const formatChatMessage = (
  message: string,
  sender: 'human' | 'ai',
  messageType: 'text' | 'code' | 'markdown' = 'text',
): ChatBoxMessage => {
  return {
    id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    message,
    messageType,
    timestamp: new Date().toISOString(),
    sender,
  };
};

/**
 * Utility function to validate chat message input
 */
export const validateChatInput = (input: string): { isValid: boolean; error?: string } => {
  if (!input || input.trim().length === 0) {
    return { isValid: false, error: 'Message cannot be empty' };
  }

  if (input.length > 4000) {
    return { isValid: false, error: 'Message is too long (max 4000 characters)' };
  }

  return { isValid: true };
};

/**
 * Interface for chat action requests
 */
export interface ChatActionRequest {
  action: 'like' | 'dislike' | 'regenerate';
  messageId: string;
  regenerationReason?: string;
}

/**
 * Custom hook for handling chat actions (like, dislike, regenerate)
 */
export const useChatActions = (chatId: string) => {
  const queryClient = useQueryClient();

  const performAction = useMutation<any, Error, ChatActionRequest>({
    mutationFn: async (actionData: ChatActionRequest) => {
      try {
        const response = await axiosInstance.post(
          '/api/platform/chat-actions',
          actionData,
          {
            params: { chatId },
          },
        );
        return response.data;
      } catch (error) {
        log.error('Error performing chat action:', error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch chat messages to update the UI
      queryClient.invalidateQueries({ queryKey: ['chatMessages', chatId] });
      log.info(`Chat action ${variables.action} performed successfully`);
    },
    onError: (error, variables) => {
      log.error(`Failed to perform chat action ${variables.action}:`, error);
    },
  });

  return {
    performAction: performAction.mutate,
    performActionAsync: performAction.mutateAsync,
    isPerformingAction: performAction.isPending,
    error: performAction.error,
    data: performAction.data,
    reset: performAction.reset,
  };
};
