import React, { useRef, useEffect, useState } from 'react';
import Textarea from '@/components/textarea';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import { PaperAirplaneIcon, ArrowUpTrayIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { Controller, useForm } from 'react-hook-form';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Divider } from '@heroui/react';
import TicketSelectionModal from '@/modules/platform/components/ticket-selection-modal';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';
import axiosInstance from '@/utils/axios';
import { useI2RPollingContext } from '../../contexts/polling.context';
import FileUploadButton from '@/components/file-upload-button';
import OptionsMenu from '../options-menu';
import { SubModules } from '@/modules/platform/interfaces/modules';

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai' | 'system';
  text: string;
  timestamp?: string;
  showActions?: boolean;
}

export interface ChatBoxProps {
  messages: ChatMessage[];
  onSend: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  onLike?: (id: string) => void;
  onDislike?: (id: string) => void;
  onRegenerate?: (id: string) => void;
  onOpenRightPanel?: () => void;
  chatId: string;
}

const ChatBox: React.FC<ChatBoxProps> = ({
  messages,
  onSend,
  onOpenRightPanel,
  chatId
}) => {
  const {
    prdData,
    setPrdData,
  } = useI2RPollingContext();
  const homepageConstants = useTranslations('I2R.homepage');
  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      message: ''
    }
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<'epics' | 'user-stories'>('epics');
  const [allAIReadyEpics, setAllAIReadyEpics] = useState<IJiraTicket[]>([]);
  const [allAIReadyStories, setAllAIReadyStories] = useState<IJiraTicket[]>([]);
  const [selectedEpics, setSelectedEpics] = useState<IJiraTicket[]>([]);
  const [selectedStories, setSelectedStories] = useState<IJiraTicket[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Watch the form field value
  const messageValue = watch('message');

  const handleSend = (data: { message: string }) => {
    if (data.message.trim()) {
      onSend(data.message.trim());
      reset();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(handleSend)();
    }
  };

  const fetchTickets = async () => {
    setIsLoading(true);
    try {
      const response = await axiosInstance.get('/api/platform/jira');
      const data = response.data || [];
      setAllAIReadyEpics(data.filter((t: IJiraTicket) => t.type.toLowerCase() === 'epic'));
      setAllAIReadyStories(data.filter((t: IJiraTicket) => t.type.toLowerCase() === 'user-story'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleJiraIconClick = () => {
    setIsTicketModalOpen(true);
    if (!allAIReadyEpics.length && !allAIReadyStories.length) {
      fetchTickets();
    }
  };

  const fetchPrd = async (chatId: string) => {
    try {
      const response = await axiosInstance.get('/api/platform/requirement-document', {
        params: { chatId: chatId, documentType: SubModules.PRD },
      });
      setPrdData(response.data);
      return response.data;
    } catch (error) {
      return null;
    }
  };

  useEffect(() => {
    if (messagesEndRef.current && typeof messagesEndRef.current.scrollIntoView === 'function') {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  useEffect(() => {
    fetchPrd(chatId);
  }, []);

  return (
    <div className="flex flex-col h-full w-full space-y-6">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto space-y-6">
        {messages.map((msg) => (
          <div key={msg.id} className="flex flex-col space-y-2">
            {msg.sender === 'user' ? (
              <div className="flex justify-end">
                <div className="bg-secondary-neutral-200 text-neutral-900 rounded-2xl px-4 py-3 max-w-md">
                  <p className="text-sm leading-relaxed">{msg.text}</p>
                </div>
              </div>
            ) : (
              <div className="flex justify-start">
                <div className="max-w-4xl w-full bg-white rounded-2xl p-4 border">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
                      <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
                    </div>
                  </div>

                  {/* Message Content */}
                  <p className="text-neutral-900 text-sm leading-relaxed">{msg.text}</p>
                  {msg.showActions && (
                    <>
                      <Divider className="my-4" />
                      <div className="flex gap-2 items-center">
                        <OptionsMenu
                          isLikeEnabled={prdData?.liked === null || !prdData?.liked}
                          isDislikeEnabled={prdData?.liked === null || prdData?.liked}
                          isRegenerateEnabled={true}
                          isEditEnabled={false}
                          showPublish={false}
                          openRegenerationModal={() => { }}
                          setRegenerationConfig={() => { }}
                          type={SubModules.PRD}
                          id={prdData.id}
                          showOpen={true}
                          onOpen={onOpenRightPanel}
                          setPrdData={setPrdData}
                          showEditButton={false}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div>
        <div className="flex items-center gap-2 mb-4">
          <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
            <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
          </div>
          <span className="text-sm text-gray-600">Hey there! Here are some next steps to consider:</span>
        </div>

        <div className="flex gap-2">
          <Button
            variant={ButtonVariant.FLAT}
            className="flex w-fit items-center gap-2 rounded-xl border px-3"
          >
            <div className="label-xs text-secondary-neutral-600">Generate Epics & User Stories</div>
          </Button>
          <Button
            variant={ButtonVariant.FLAT}
            className="flex w-fit items-center gap-2 rounded-xl border px-3"
          >
            <div className="label-xs text-secondary-neutral-600">Regenerate PRD</div>
          </Button>
        </div>
      </div>

      {/* Input Section */}
      <div className="border border-gray-200 rounded-xl bg-white">
        <Controller
          name="message"
          control={control}
          rules={{ required: homepageConstants('inputs.idea.error') }}
          render={({ field }) => (
            <div className="relative w-full">
              <Textarea
                {...field}
                placeholder={homepageConstants('inputs.idea.placeholder')}
                className="rounded-xl border-none w-full resize-none focus:ring-0"
                onKeyDown={handleKeyDown}
                onKeyUp={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                  }
                }}
              />
              <div className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center gap-2 mt-2">
                <Image className='cursor-pointer' src="/icons/jira.svg" alt="jira logo" width={20} height={20} onClick={handleJiraIconClick} />
                <FileUploadButton
                  icon={<ArrowUpTrayIcon className="h-6 w-6 text-secondary-neutral-400" />}
                  onFileSelect={(file) => {
                    console.log('Selected file:', file);
                  }}
                />
              </div>
              <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center mt-2">
                <PaperAirplaneIcon className={`h-6 w-6 cursor-pointer ${messageValue?.trim() ? 'text-primary-teal-600' : 'text-secondary-neutral-400'}`}
                  onClick={handleSubmit(handleSend)} />
              </div>
            </div>
          )}
        />
      </div>

      <TicketSelectionModal
        title="Select Jira Tickets"
        noTicketsError="No tickets found"
        maxLimitError="You can select up to 5 tickets"
        isOpen={isTicketModalOpen}
        onClose={() => setIsTicketModalOpen(false)}
        selectedOption={selectedOption}
        setSelectedOption={setSelectedOption}
        allAIReadyEpics={allAIReadyEpics}
        allAIReadyStories={allAIReadyStories}
        selectedEpics={selectedEpics}
        selectedStories={selectedStories}
        setSelectedEpics={setSelectedEpics}
        setSelectedStories={setSelectedStories}
        isLoading={isLoading}
      />
    </div>
  );
};

export default ChatBox; 